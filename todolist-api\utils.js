/**
 * 滴答清单API工具函数
 * 提供通用的辅助函数和数据处理方法
 */

const { ERROR_CODES, SUCCESS_RESPONSE, ERROR_RESPONSE } = require('./config.js')

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {any} data - 响应数据
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(message = "操作成功", data = null) {
	return {
		errCode: null,
		errMsg: message,
		data: data
	}
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = ERROR_CODES.UNKNOWN_ERROR, errMsg = "操作失败", details = null) {
	return {
		errCode: errCode,
		errMsg: errMsg,
		details: details
	}
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 如果校验失败返回错误响应，否则返回null
 */
function validateParams(params, requiredFields) {
	for (const field of requiredFields) {
		if (!params[field] || (typeof params[field] === 'string' && !params[field].trim())) {
			return createErrorResponse(
				ERROR_CODES.PARAM_IS_NULL,
				`参数 ${field} 不能为空`
			)
		}
	}
	return null
}

/**
 * 格式化日期为API所需格式
 * @param {string|Date} date - 日期
 * @returns {string} ISO格式的日期字符串
 */
function formatDateForApi(date) {
	try {
		if (!date) return null
		
		const dateObj = typeof date === 'string' ? new Date(date) : date
		if (isNaN(dateObj.getTime())) {
			throw new Error('Invalid date')
		}
		
		return dateObj.toISOString()
	} catch (error) {
		console.error('日期格式化失败：', error)
		return null
	}
}

/**
 * 格式化日期为可读格式
 * @param {string} isoDate - ISO格式日期字符串
 * @returns {string} 可读格式的日期字符串
 */
function formatDateForDisplay(isoDate) {
	try {
		if (!isoDate) return null
		
		const date = new Date(isoDate)
		if (isNaN(date.getTime())) {
			return isoDate
		}
		
		return date.toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit'
		})
	} catch (error) {
		console.error('日期显示格式化失败：', error)
		return isoDate
	}
}

/**
 * 简化任务数据
 * @param {object} task - 原始任务数据
 * @param {Array} projects - 项目列表
 * @returns {object} 简化后的任务数据
 */
function simplifyTaskData(task, projects = []) {
	if (!task) return null
	
	const project = projects.find(p => p.id === task.projectId)
	
	return {
		id: task.id,
		title: task.title,
		content: task.content,
		priority: task.priority,
		status: task.status,
		isCompleted: task.status === 2 || task.isCompleted,
		projectId: task.projectId,
		projectName: project ? project.name : null,
		tags: task.tags || [],
		startDate: task.startDate,
		dueDate: task.dueDate,
		isAllDay: task.isAllDay,
		reminder: task.reminder,
		createdTime: task.createdTime,
		modifiedTime: task.modifiedTime,
		kind: task.kind
	}
}

/**
 * 简化项目数据
 * @param {object} project - 原始项目数据
 * @returns {object} 简化后的项目数据
 */
function simplifyProjectData(project) {
	if (!project) return null
	
	return {
		id: project.id,
		name: project.name,
		color: project.color,
		kind: project.kind,
		isOwner: project.isOwner,
		closed: project.closed,
		createdTime: project.createdTime,
		modifiedTime: project.modifiedTime,
		taskCount: project.taskCount || 0
	}
}

/**
 * 解析HTTP响应
 * @param {object} response - HTTP响应对象
 * @returns {object} 解析后的响应数据
 */
function parseHttpResponse(response) {
	try {
		// 检查响应状态
		if (response.status >= 400) {
			let errorMsg = `HTTP ${response.status}`
			try {
				const errorData = typeof response.data === 'string' 
					? JSON.parse(response.data) 
					: response.data
				if (errorData && typeof errorData === 'object') {
					errorMsg += `: ${JSON.stringify(errorData)}`
				} else {
					errorMsg += `: ${errorData}`
				}
			} catch {
				errorMsg += `: ${response.data}`
			}
			
			return createErrorResponse(
				ERROR_CODES.API_ERROR,
				errorMsg,
				response.data
			)
		}

		// 解析响应数据
		let responseData
		if (response.data) {
			try {
				responseData = typeof response.data === 'string' 
					? JSON.parse(response.data) 
					: response.data
			} catch (parseError) {
				return createErrorResponse(
					ERROR_CODES.PARSE_ERROR,
					'响应数据解析失败',
					response.data
				)
			}
		} else {
			responseData = {}
		}

		return createSuccessResponse('请求成功', responseData)

	} catch (error) {
		return createErrorResponse(
			ERROR_CODES.UNKNOWN_ERROR,
			error.message || '响应解析失败',
			error
		)
	}
}

/**
 * 从cookies中提取token
 * @param {Array} cookies - cookies数组
 * @returns {string|null} token值或null
 */
function extractTokenFromCookies(cookies) {
	if (!cookies || !Array.isArray(cookies)) {
		return null
	}
	
	for (const cookie of cookies) {
		const match = cookie.match(/t=([^;]+)/)
		if (match) {
			return match[1]
		}
	}
	
	return null
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID字符串
 */
function generateUniqueId() {
	return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
function deepClone(obj) {
	if (obj === null || typeof obj !== 'object') {
		return obj
	}
	
	if (obj instanceof Date) {
		return new Date(obj.getTime())
	}
	
	if (obj instanceof Array) {
		return obj.map(item => deepClone(item))
	}
	
	if (typeof obj === 'object') {
		const cloned = {}
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key])
			}
		}
		return cloned
	}
	
	return obj
}

/**
 * 移除对象中的空值字段
 * @param {object} obj - 要处理的对象
 * @returns {object} 处理后的对象
 */
function removeEmptyFields(obj) {
	const result = {}
	
	for (const key in obj) {
		if (obj.hasOwnProperty(key)) {
			const value = obj[key]
			if (value !== null && value !== undefined && value !== '') {
				result[key] = value
			}
		}
	}
	
	return result
}

module.exports = {
	createSuccessResponse,
	createErrorResponse,
	validateParams,
	formatDateForApi,
	formatDateForDisplay,
	simplifyTaskData,
	simplifyProjectData,
	parseHttpResponse,
	extractTokenFromCookies,
	generateUniqueId,
	deepClone,
	removeEmptyFields
}
