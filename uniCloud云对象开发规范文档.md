# uniCloud 云对象开发规范文档

## 概述

本文档基于 uniCloud 官方文档和项目现有代码分析，提供了一套完整的云对象开发规范和模板，用于指导 AI 在其他项目中生成符合项目规范的云对象代码。

## 1. 文件结构规范

### 1.1 标准文件结构

```
云对象名称/
├── index.obj.js          # 主要的云对象代码（必需）
├── package.json          # 依赖配置（必需）
├── README.md            # 功能说明文档（推荐）
├── test.js              # 本地测试脚本（推荐）
├── 云对象名称.param.js   # 运行参数配置（开发调试用）
├── config.js            # 配置文件（如有需要）
├── utils.js             # 工具函数（如有需要）
└── modules/             # 模块化组件目录（复杂项目）
    ├── validator.js     # 参数验证模块
    ├── handler.js       # 业务处理模块
    └── ...
```

### 1.2 文件命名规范

- **云对象目录名**：使用 kebab-case 格式，如 `dida-api`、`user-service`
- **入口文件**：固定为 `index.obj.js`
- **配置文件**：使用 camelCase 格式，如 `config.js`、`utils.js`
- **参数文件**：格式为 `{云对象名}.param.js`

## 2. 云对象代码结构规范

### 2.1 基础模板

```javascript
// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

/**
 * [云对象名称] 云对象
 * [云对象功能描述]
 */
module.exports = {
	_before: function () {
		// 通用预处理器
		// 可在此添加通用的预处理逻辑，如日志记录、权限验证等
	},

	/**
	 * 方法描述
	 * @param {string} param1 参数 1 描述
	 * @param {number} param2 参数 2 描述
	 * @returns {object} 返回值描述
	 */
	async methodName(param1, param2) {
		// 参数校验
		if (!param1) {
			return {
				errCode: "PARAM_IS_NULL",
				errMsg: "参数 1 不能为空",
			};
		}

		try {
			// 业务逻辑处理

			// 成功返回
			return {
				errCode: null,
				errMsg: "操作成功",
				data: {
					// 返回数据
				},
			};
		} catch (error) {
			// 错误处理
			console.error("方法执行异常：", error);
			return {
				errCode: "UNKNOWN_ERROR",
				errMsg: error.message || "操作失败",
				details: error,
			};
		}
	},
};
```

### 3.2 内置特殊方法

#### 3.2.1 \_before 预处理器

```javascript
_before: function () {
  // 获取客户端信息
  const clientInfo = this.getClientInfo()

  // 权限验证示例
  const methodName = this.getMethodName()
  if (methodName === 'sensitiveMethod' && !this.getUniIdToken()) {
    throw new Error('需要登录后才能访问')
  }

  // 日志记录
  console.log(`调用方法：${methodName}, 客户端：${clientInfo.clientIP}`)
}
```

#### 3.2.2 \_after 后处理器

```javascript
_after: function(error, result) {
  if (error) {
    // 错误日志记录
    console.error('方法执行错误：', error)
    throw error
  }

  // 添加通用响应字段
  result.timestamp = Date.now()
  result.requestId = this.getUniCloudRequestId()

  return result
}
```

## 3. 参数验证规范

### 3.1 基础参数验证

```javascript
// 必需参数验证
if (!param) {
	return {
		errCode: "PARAM_IS_NULL",
		errMsg: "参数不能为空",
	};
}

// 参数类型验证
if (typeof param !== "string") {
	return {
		errCode: "PARAM_TYPE_ERROR",
		errMsg: "参数类型错误，期望字符串",
	};
}

// 参数格式验证
if (!/^[\w\.-]+@[\w\.-]+\.\w+$/.test(email)) {
	return {
		errCode: "PARAM_FORMAT_ERROR",
		errMsg: "邮箱格式不正确",
	};
}
```

### 3.2 复杂参数验证

```javascript
// 对象参数验证
if (!params || typeof params !== "object") {
	return {
		errCode: "PARAM_IS_NULL",
		errMsg: "参数对象不能为空",
	};
}

const { username, password, email } = params;

// 批量验证
const requiredFields = { username, password, email };
for (const [key, value] of Object.entries(requiredFields)) {
	if (!value || !value.trim()) {
		return {
			errCode: "PARAM_IS_NULL",
			errMsg: `${key}不能为空`,
		};
	}
}
```

## 4. 返回值格式规范

### 4.1 成功响应格式

```javascript
// 标准成功响应
{
  errCode: null,           // 错误代码，成功时为 null
  errMsg: '操作成功',       // 消息描述
  data: {                  // 返回数据
    // 具体数据内容
  }
}

// 简化成功响应（无数据返回）
{
  errCode: null,
  errMsg: '操作成功'
}
```

### 4.2 错误响应格式

```javascript
// 标准错误响应
{
  errCode: 'ERROR_CODE',   // 错误代码
  errMsg: '错误描述',       // 错误消息
  details: {}              // 详细错误信息（可选）
}
```

## 5. JSDoc 注释规范

### 5.1 方法注释模板

```javascript
/**
 * 方法功能描述
 *
 * 详细说明（可选）
 *
 * @param {string} param1 - 参数 1 描述
 * @param {number} param2 - 参数 2 描述，可选参数
 * @param {object} options - 配置选项
 * @param {boolean} options.flag - 选项标志
 * @returns {Promise<object>} 返回值描述
 *
 * @example
 * // 使用示例
 * const result = await cloudObj.methodName('value1', 123, { flag: true })
 *
 * @throws {Error} 抛出错误的情况描述
 * @since 1.0.0
 */
```

### 5.2 参数类型说明

| JSDoc 类型          | 说明           | 示例                           |
| ------------------- | -------------- | ------------------------------ |
| `{string}`          | 字符串         | `@param {string} name`         |
| `{number}`          | 数字           | `@param {number} age`          |
| `{boolean}`         | 布尔值         | `@param {boolean} isActive`    |
| `{object}`          | 对象           | `@param {object} config`       |
| `{Array}`           | 数组           | `@param {Array} items`         |
| `{Array<string>}`   | 字符串数组     | `@param {Array<string>} names` |
| `{string\|null}`    | 可为空的字符串 | `@param {string\|null} value`  |
| `{Promise<object>}` | Promise 对象   | `@returns {Promise<object>}`   |

## 6. HTTP 请求规范

### 6.1 外部 API 调用

```javascript
async apiMethod() {
  try {
    const response = await uniCloud.httpclient.request(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (compatible; uniCloud/1.0)',
        'Authorization': `Bearer ${token}`
      },
      data: requestData,
      timeout: 10000 // 10 秒超时
    })

    // 检查响应状态
    if (response.status !== 200) {
      return {
        errCode: 'API_ERROR',
        errMsg: `API 请求失败，状态码：${response.status}`,
        details: response.data
      }
    }

    // 解析响应数据
    let responseData
    try {
      responseData = typeof response.data === 'string'
        ? JSON.parse(response.data)
        : response.data
    } catch (parseError) {
      return {
        errCode: 'PARSE_ERROR',
        errMsg: '响应数据解析失败',
        details: response.data
      }
    }

    return {
      errCode: null,
      errMsg: '请求成功',
      data: responseData
    }

  } catch (error) {
    console.error('HTTP 请求异常：', error)

    // 处理不同类型的错误
    if (error.code === 'TIMEOUT') {
      return {
        errCode: 'TIMEOUT_ERROR',
        errMsg: '请求超时，请稍后重试'
      }
    }

    return {
      errCode: 'NETWORK_ERROR',
      errMsg: error.message || 'HTTP 请求失败',
      details: error
    }
  }
}
```

## 7. 配置文件规范

### 7.1 package.json 配置

```json
{
	"name": "云对象名称",
	"version": "1.0.0",
	"description": "云对象功能描述",
	"dependencies": {
		// 根据需要添加依赖
	},
	"extensions": {
		"uni-cloud-jql": {}
	}
}
```

### 7.2 配置文件模式

```javascript
// config.js
module.exports = {
	// API 配置
	api: {
		baseURL: process.env.API_BASE_URL || "https://api.example.com",
		timeout: parseInt(process.env.API_TIMEOUT) || 10000,
		apiKey: process.env.API_KEY || "default-key",
	},

	// 数据库配置
	database: {
		timeout: 5000,
		retryTimes: 3,
	},

	// 业务配置
	business: {
		maxRetries: 3,
		defaultPageSize: 20,
	},
};
```

## 8. 文档规范

### 8.1 README.md 模板

````markdown
# 云对象名称

云对象功能的简要描述。

## 功能列表

### 1. 方法名称 (methodName)

方法功能描述。

#### 参数

| 参数名 | 类型   | 必需 | 说明        |
| ------ | ------ | ---- | ----------- |
| param1 | string | 是   | 参数 1 描述 |
| param2 | number | 否   | 参数 2 描述 |

#### 返回值

**成功响应:**

```json
{
	"errCode": null,
	"errMsg": "操作成功",
	"data": {
		// 返回数据结构
	}
}
```
````

**错误响应：**

```json
{
	"errCode": "错误代码",
	"errMsg": "错误描述",
	"details": "详细错误信息（可选）"
}
```

#### 错误代码说明

| 错误代码       | 说明         |
| -------------- | ------------ |
| PARAM_IS_NULL  | 参数为空     |
| BUSINESS_ERROR | 业务逻辑错误 |

## 使用示例

### 前端调用示例

```javascript
// 在 Vue 组件中调用
export default {
	methods: {
		async callMethod() {
			try {
				const cloudObj = uniCloud.importObject("云对象名称");
				const result = await cloudObj.methodName("param1", 123);

				if (result.errCode) {
					uni.showToast({
						title: result.errMsg,
						icon: "none",
					});
					return;
				}

				// 处理成功结果
				console.log("操作成功：", result.data);
			} catch (error) {
				console.error("调用失败：", error);
				uni.showToast({
					title: "网络错误，请稍后重试",
					icon: "none",
				});
			}
		},
	},
};
```

## 注意事项

1. **安全性**: 相关安全注意事项
2. **性能**: 性能相关说明
3. **限制**: 使用限制说明

````

### 8.2 部署文档模板

```markdown
# 云对象部署指南

## 部署步骤

### 1. 检查代码
确保所有文件完整且语法正确。

### 2. 上传云对象
在 HBuilderX 中右键点击云对象目录，选择"上传并运行云函数"。

### 3. 验证部署
通过测试脚本或前端页面验证功能正常。

## 配置说明

### 环境变量
如需配置环境变量，在云对象中添加相应配置。

### 依赖管理
确保 package.json 中的依赖正确配置。

## 故障排除

### 常见问题
1. 部署失败：检查网络连接和代码语法
2. 调用失败：检查参数格式和权限设置
3. 性能问题：优化数据库查询和网络请求
````

## 9. 最佳实践

### 9.1 代码组织

1. **单一职责**：每个方法只负责一个具体功能
2. **模块化**：复杂逻辑拆分为独立模块
3. **可复用**：提取公共逻辑为工具函数
4. **可测试**：编写完整的测试用例

### 9.2 性能优化

1. **数据库优化**：

   - 合理使用索引
   - 避免全表扫描
   - 控制查询数量

2. **网络请求优化**：

   - 设置合理的超时时间
   - 实现重试机制
   - 缓存常用数据

3. **内存管理**：
   - 及时释放大对象
   - 避免内存泄漏
   - 控制并发数量

### 9.3 安全规范

1. **输入验证**：严格验证所有输入参数
2. **权限控制**：实现细粒度的权限验证
3. **敏感信息**：不在日志中输出敏感数据
4. **错误处理**：避免暴露系统内部信息

### 9.4 日志规范

```javascript
// 统一日志格式
console.log(`[${new Date().toISOString()}] [${this.getMethodName()}] 操作开始`);
console.log(`[${new Date().toISOString()}] [${this.getMethodName()}] 操作完成`);
console.error(
	`[${new Date().toISOString()}] [${this.getMethodName()}] 操作失败:`,
	error
);
```

## 10. 完整示例

### 10.1 用户管理云对象示例

```javascript
// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

const uniID = require("uni-id-common");

/**
 * 用户管理云对象
 * 提供用户注册、登录、信息管理等功能
 */
module.exports = {
	_before: function () {
		// 创建 uni-id 实例
		this.uniID = uniID.createInstance({
			clientInfo: this.getClientInfo(),
		});

		// 记录请求日志
		const methodName = this.getMethodName();
		const clientInfo = this.getClientInfo();
		console.log(
			`[${new Date().toISOString()}] 调用方法: ${methodName}, 客户端: ${
				clientInfo.clientIP
			}`
		);
	},

	/**
	 * 用户注册
	 * @param {string} username 用户名
	 * @param {string} password 密码
	 * @param {string} email 邮箱
	 * @returns {object} 注册结果
	 */
	async register(username, password, email) {
		// 参数验证
		if (!username || !password || !email) {
			return {
				errCode: "PARAM_IS_NULL",
				errMsg: "用户名、密码和邮箱不能为空",
			};
		}

		// 邮箱格式验证
		if (!/^[\w\.-]+@[\w\.-]+\.\w+$/.test(email)) {
			return {
				errCode: "PARAM_FORMAT_ERROR",
				errMsg: "邮箱格式不正确",
			};
		}

		try {
			// 调用 uni-id 注册方法
			const result = await this.uniID.registerUser({
				username,
				password,
				email,
			});

			if (result.errCode === 0) {
				return {
					errCode: null,
					errMsg: "注册成功",
					data: {
						uid: result.uid,
						token: result.token,
					},
				};
			} else {
				return {
					errCode: "REGISTER_FAILED",
					errMsg: result.errMsg || "注册失败",
					details: result,
				};
			}
		} catch (error) {
			console.error("注册异常:", error);
			return {
				errCode: "UNKNOWN_ERROR",
				errMsg: "注册过程中发生错误",
				details: error,
			};
		}
	},

	/**
	 * 用户登录
	 * @param {string} username 用户名
	 * @param {string} password 密码
	 * @returns {object} 登录结果
	 */
	async login(username, password) {
		// 参数验证
		if (!username || !password) {
			return {
				errCode: "PARAM_IS_NULL",
				errMsg: "用户名和密码不能为空",
			};
		}

		try {
			const result = await this.uniID.loginByUsernamePassword({
				username,
				password,
			});

			if (result.errCode === 0) {
				return {
					errCode: null,
					errMsg: "登录成功",
					data: {
						uid: result.uid,
						token: result.token,
						userInfo: result.userInfo,
					},
				};
			} else {
				return {
					errCode: "LOGIN_FAILED",
					errMsg: result.errMsg || "登录失败",
					details: result,
				};
			}
		} catch (error) {
			console.error("登录异常:", error);
			return {
				errCode: "UNKNOWN_ERROR",
				errMsg: "登录过程中发生错误",
				details: error,
			};
		}
	},

	/**
	 * 获取用户信息
	 * @returns {object} 用户信息
	 */
	async getUserInfo() {
		try {
			const token = this.getUniIdToken();
			if (!token) {
				return {
					errCode: "UNAUTHORIZED",
					errMsg: "请先登录",
				};
			}

			const result = await this.uniID.checkToken(token);
			if (result.errCode === 0) {
				return {
					errCode: null,
					errMsg: "获取成功",
					data: result.userInfo,
				};
			} else {
				return {
					errCode: "UNAUTHORIZED",
					errMsg: "登录状态已失效",
				};
			}
		} catch (error) {
			console.error("获取用户信息异常:", error);
			return {
				errCode: "UNKNOWN_ERROR",
				errMsg: "获取用户信息失败",
				details: error,
			};
		}
	},
};
```

## 11. 总结

本规范文档提供了完整的 uniCloud 云对象开发指南，包括：

1. **基础概念**：云对象的核心概念和优势
2. **文件结构**：标准的项目文件组织方式
3. **代码规范**：统一的代码编写标准
4. **错误处理**：完善的错误处理机制
5. **文档规范**：详细的文档编写要求
6. **最佳实践**：性能优化和安全规范
7. **完整示例**：实际可用的代码示例

遵循本规范可以确保生成的云对象代码具有良好的可读性、可维护性和可扩展性，同时保持与项目整体架构的一致性。

### 6.1 外部 API 调用

```javascript
async apiMethod() {
  try {
    const response = await uniCloud.httpclient.request(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (compatible; uniCloud/1.0)',
        'Authorization': `Bearer ${token}`
      },
      data: requestData,
      timeout: 10000 // 10秒超时
    })

    // 检查响应状态
    if (response.status !== 200) {
      return {
        errCode: 'API_ERROR',
        errMsg: `API请求失败，状态码: ${response.status}`,
        details: response.data
      }
    }

    // 解析响应数据
    let responseData
    try {
      responseData = typeof response.data === 'string'
        ? JSON.parse(response.data)
        : response.data
    } catch (parseError) {
      return {
        errCode: 'PARSE_ERROR',
        errMsg: '响应数据解析失败',
        details: response.data
      }
    }

    return {
      errCode: null,
      errMsg: '请求成功',
      data: responseData
    }

  } catch (error) {
    console.error('HTTP 请求异常:', error)

    // 处理不同类型的错误
    if (error.code === 'TIMEOUT') {
      return {
        errCode: 'TIMEOUT_ERROR',
        errMsg: '请求超时，请稍后重试'
      }
    }

    return {
      errCode: 'NETWORK_ERROR',
      errMsg: error.message || 'HTTP请求失败',
      details: error
    }
  }
}
```
