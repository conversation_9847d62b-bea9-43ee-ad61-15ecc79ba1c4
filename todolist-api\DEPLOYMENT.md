# 滴答清单API云对象部署指南

本文档详细说明如何将滴答清单API云对象部署到uniCloud环境。

## 前置条件

1. **uniCloud账号**: 需要有效的uniCloud开发者账号
2. **HBuilderX**: 安装最新版本的HBuilderX开发工具
3. **uni-app项目**: 创建或使用现有的uni-app项目
4. **滴答清单账号**: 用于测试API功能的有效账号

## 部署步骤

### 1. 创建uniCloud项目

1. 在HBuilderX中创建新的uni-app项目
2. 在项目根目录右键选择"创建uniCloud云开发环境"
3. 选择阿里云或腾讯云作为服务商
4. 创建云服务空间

### 2. 上传云对象文件

1. 在项目的 `uniCloud/cloudfunctions` 目录下创建以下云对象目录：
   ```
   uniCloud/cloudfunctions/
   ├── todolist-api/
   ├── task/
   └── project/
   ```

2. 将对应的文件复制到相应目录：
   ```
   todolist-api/ -> uniCloud/cloudfunctions/todolist-api/
   ├── index.obj.js
   ├── config.js
   ├── utils.js
   └── package.json
   
   task.obj.js -> uniCloud/cloudfunctions/task/index.obj.js
   project.obj.js -> uniCloud/cloudfunctions/project/index.obj.js
   ```

3. 为每个云对象目录创建 `package.json` 文件（如果没有的话）

### 3. 配置云对象

在每个云对象目录中，确保 `package.json` 包含正确的配置：

```json
{
  "name": "todolist-api",
  "version": "1.0.0",
  "description": "滴答清单API云对象",
  "main": "index.obj.js",
  "dependencies": {},
  "engines": {
    "node": ">=14.0.0"
  }
}
```

### 4. 上传并部署

1. 在HBuilderX中右键点击云对象目录
2. 选择"上传并运行"
3. 等待上传完成并部署成功

### 5. 配置网络访问权限

在uniCloud控制台中：

1. 进入对应的云服务空间
2. 找到"云函数/云对象"管理页面
3. 为每个云对象配置网络访问权限
4. 添加滴答清单API域名到白名单：
   - `api.dida365.com`
   - `dida365.com`

### 6. 测试部署

创建测试页面验证功能：

```javascript
// pages/test/test.vue
<template>
  <view class="container">
    <button @click="testLogin">测试登录</button>
    <button @click="testTasks">测试任务</button>
    <text>{{ result }}</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      result: '',
      token: ''
    }
  },
  methods: {
    async testLogin() {
      try {
        const todolistApi = uniCloud.importObject('todolist-api')
        const result = await todolistApi.login('your-phone', 'your-password', true)
        
        if (result.errCode) {
          this.result = '登录失败：' + result.errMsg
        } else {
          this.token = result.data.token
          this.result = '登录成功，Token：' + this.token.substring(0, 10) + '...'
        }
      } catch (error) {
        this.result = '登录异常：' + error.message
      }
    },
    
    async testTasks() {
      if (!this.token) {
        this.result = '请先登录获取Token'
        return
      }
      
      try {
        const taskApi = uniCloud.importObject('task')
        await taskApi.initWithToken(this.token)
        
        const result = await taskApi.getTasks()
        if (result.errCode) {
          this.result = '获取任务失败：' + result.errMsg
        } else {
          this.result = '获取任务成功，数量：' + result.data.length
        }
      } catch (error) {
        this.result = '任务测试异常：' + error.message
      }
    }
  }
}
</script>
```

## 环境配置

### 开发环境

1. 在 `config.js` 中设置 `debug: true`
2. 启用详细的日志输出
3. 使用测试账号进行功能验证

### 生产环境

1. 设置 `debug: false`
2. 配置适当的错误处理
3. 实施访问频率限制
4. 添加用户权限验证

## 安全配置

### 1. 访问控制

```javascript
// 在云对象的 _before 方法中添加访问控制
_before: function() {
  // 获取客户端信息
  const clientInfo = this.getClientInfo()
  
  // 检查来源
  if (!this.isValidClient(clientInfo)) {
    throw new Error('访问被拒绝')
  }
  
  // 记录访问日志
  console.log(`访问记录: ${clientInfo.clientIP} - ${this.getMethodName()}`)
}
```

### 2. 频率限制

```javascript
// 实现简单的频率限制
const accessLog = new Map()

function checkRateLimit(clientIP) {
  const now = Date.now()
  const key = clientIP
  const record = accessLog.get(key) || { count: 0, resetTime: now + 60000 }
  
  if (now > record.resetTime) {
    record.count = 1
    record.resetTime = now + 60000
  } else {
    record.count++
  }
  
  accessLog.set(key, record)
  
  if (record.count > 100) { // 每分钟最多100次请求
    throw new Error('请求频率过高')
  }
}
```

### 3. 数据加密

对敏感数据进行加密存储：

```javascript
const crypto = require('crypto')

function encryptToken(token) {
  const cipher = crypto.createCipher('aes192', 'your-secret-key')
  let encrypted = cipher.update(token, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  return encrypted
}

function decryptToken(encryptedToken) {
  const decipher = crypto.createDecipher('aes192', 'your-secret-key')
  let decrypted = decipher.update(encryptedToken, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  return decrypted
}
```

## 监控和日志

### 1. 错误监控

```javascript
// 在云对象中添加错误监控
function logError(error, context) {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    context: context,
    timestamp: new Date().toISOString(),
    clientInfo: this.getClientInfo()
  }
  
  // 发送到日志服务或数据库
  console.error('云对象错误:', JSON.stringify(errorInfo))
}
```

### 2. 性能监控

```javascript
// 添加性能监控
function performanceMonitor(methodName, startTime) {
  const endTime = Date.now()
  const duration = endTime - startTime
  
  console.log(`性能监控: ${methodName} 耗时 ${duration}ms`)
  
  if (duration > 5000) { // 超过5秒的请求
    console.warn(`慢查询警告: ${methodName} 耗时 ${duration}ms`)
  }
}
```

## 故障排除

### 常见问题

1. **网络访问被拒绝**
   - 检查云服务空间的网络配置
   - 确认域名白名单设置正确

2. **Token失效**
   - 检查登录逻辑
   - 确认Cookie解析正确

3. **数据解析错误**
   - 检查API响应格式
   - 确认JSON解析逻辑

4. **超时错误**
   - 调整请求超时时间
   - 检查网络连接状况

### 调试技巧

1. 启用详细日志输出
2. 使用云函数日志查看运行状态
3. 在本地环境模拟测试
4. 使用Postman等工具测试API

## 维护和更新

### 版本管理

1. 使用语义化版本号
2. 记录每次更新的变更内容
3. 保持向后兼容性

### 定期维护

1. 定期检查API变更
2. 更新依赖包版本
3. 清理过期日志和缓存
4. 监控系统性能指标

## 支持和反馈

如果在部署过程中遇到问题，请：

1. 检查本文档的故障排除部分
2. 查看uniCloud官方文档
3. 在项目仓库中提交Issue
4. 联系技术支持团队

---

**注意**: 请确保在生产环境中妥善保管用户凭证，遵循相关的数据保护法规。
