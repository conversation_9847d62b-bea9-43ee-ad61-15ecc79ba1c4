# 滴答清单 API 云对象

基于 uniCloud 云对象开发规范，将滴答清单 MCP 工具项目重构为云对象形式，提供完整的滴答清单 API 功能。

## 项目结构

```
todolist-api/
├── index.obj.js          # 云对象入口文件 - 唯一的云对象入口
├── modules/              # 功能模块目录
│   ├── authManager.js    # 认证管理模块
│   ├── taskManager.js    # 任务管理模块
│   └── projectManager.js # 项目管理模块
├── config.js             # 配置文件 - API常量和配置信息
├── utils.js              # 工具函数 - 通用辅助函数
├── package.json          # 项目配置
├── test.js               # 测试文件
├── DEPLOYMENT.md         # 部署指南
└── README.md             # 说明文档
```

## 架构设计

### 符合 uniCloud 规范的单一入口设计

本项目严格遵循 uniCloud 云对象开发规范：

1. **单一入口文件**: `index.obj.js` 是唯一的云对象入口文件
2. **模块化组织**: 通过 `modules/` 目录下的模块文件组织功能代码
3. **工厂模式**: 各模块导出工厂函数，接收云对象实例作为参数
4. **共享上下文**: 模块间可以共享云对象的 `this.token`、`this.headers` 等实例变量

## 功能特性

### 🔐 认证功能

- 用户登录（手机号/邮箱）
- Token 管理和初始化
- 自动请求头设置

### 📝 任务管理

- 获取任务列表（支持多种筛选条件）
- 创建任务
- 更新任务
- 删除任务
- 完成/取消完成任务
- 批量操作任务

### 📁 项目管理

- 获取项目列表
- 创建项目
- 更新项目
- 删除项目
- 关闭/重新打开项目

### 🛠 工具功能

- 统一的错误处理
- 数据格式化和简化
- 参数校验
- 日期处理

## 使用方法

### 1. 认证登录

```javascript
// 使用主云对象进行登录
const todolistApi = uniCloud.importObject("todolist-api");

// 手机号登录
const loginResult = await todolistApi.login("13800138000", "password", true);
if (loginResult.errCode) {
	console.error("登录失败：", loginResult.errMsg);
} else {
	console.log("登录成功，token：", loginResult.data.token);
}

// 邮箱登录
const emailLoginResult = await todolistApi.login(
	"<EMAIL>",
	"password",
	false
);
```

### 2. 任务管理

```javascript
// 导入任务管理云对象
const taskApi = uniCloud.importObject("task");

// 初始化（使用登录获得的token）
await taskApi.initWithToken(token);

// 获取任务列表
const tasksResult = await taskApi.getTasks("all", null, null, null, false);
if (!tasksResult.errCode) {
	console.log("任务列表：", tasksResult.data);
}

// 创建任务
const createResult = await taskApi.createTask(
	"新任务标题",
	"任务内容",
	3, // 中等优先级
	"工作项目",
	["重要", "紧急"],
	"2024-01-01 09:00",
	"2024-01-01 18:00"
);

// 完成任务
const completeResult = await taskApi.completeTask("task-id");

// 批量删除任务
const batchResult = await taskApi.batchOperateTasks(["id1", "id2"], "delete");
```

### 3. 项目管理

```javascript
// 导入项目管理云对象
const projectApi = uniCloud.importObject("project");

// 初始化
await projectApi.initWithToken(token);

// 获取项目列表
const projectsResult = await projectApi.getProjects();

// 创建项目
const createProjectResult = await projectApi.createProject(
	"新项目",
	"#3498db", // 蓝色
	"TASK" // 任务类型项目
);

// 关闭项目
const closeResult = await projectApi.closeProject("project-id");
```

## API 响应格式

所有 API 都遵循统一的响应格式：

### 成功响应

```javascript
{
    errCode: null,
    errMsg: "操作成功",
    data: { /* 具体数据 */ }
}
```

### 错误响应

```javascript
{
    errCode: "ERROR_CODE",
    errMsg: "错误描述",
    details: { /* 错误详情 */ }
}
```

## 错误码说明

| 错误码            | 说明         |
| ----------------- | ------------ |
| PARAM_IS_NULL     | 参数为空     |
| PARAM_INVALID     | 参数无效     |
| UNAUTHORIZED      | 未授权       |
| LOGIN_ERROR       | 登录失败     |
| TOKEN_NOT_FOUND   | Token 未找到 |
| NETWORK_ERROR     | 网络错误     |
| TIMEOUT_ERROR     | 请求超时     |
| API_ERROR         | API 错误     |
| PARSE_ERROR       | 数据解析错误 |
| PROJECT_NOT_FOUND | 项目未找到   |
| TASK_NOT_FOUND    | 任务未找到   |
| UNKNOWN_ERROR     | 未知错误     |

## 配置说明

### API 配置 (config.js)

- BASE_URL: 滴答清单 API 基础 URL
- TIMEOUT: 请求超时时间
- DEFAULT_HEADERS: 默认请求头

### 任务配置

- STATUS: 任务状态常量
- PRIORITY: 优先级常量
- KIND: 任务类型常量
- FILTER_MODE: 筛选模式常量

### 项目配置

- KIND: 项目类型常量
- COLORS: 预定义颜色

## 开发规范

1. **错误处理**: 所有方法都应该返回统一的响应格式
2. **参数校验**: 使用 `validateParams` 进行参数校验
3. **日志记录**: 在 `_before` 方法中记录调用日志
4. **数据简化**: 使用工具函数简化返回的数据结构
5. **异常捕获**: 所有异步操作都应该包含 try-catch

## 部署说明

1. 将整个 `todolist-api` 目录上传到 uniCloud 云函数
2. 在 uniCloud 控制台中配置云对象
3. 确保网络访问权限允许访问滴答清单 API
4. 测试各个功能模块

## 注意事项

1. 需要有效的滴答清单账号进行测试
2. API 调用频率请遵循滴答清单的限制
3. Token 有效期有限，需要定期重新登录
4. 生产环境中请妥善保管用户凭证

## 更新日志

### v1.0.0 (2024-01-01)

- 初始版本发布
- 完成认证、任务管理、项目管理功能
- 提供完整的错误处理和数据格式化
