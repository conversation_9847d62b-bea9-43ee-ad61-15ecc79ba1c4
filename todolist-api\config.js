/**
 * 滴答清单API配置文件
 * 包含API相关的常量和配置信息
 */

// API基础配置
const API_CONFIG = {
	// 滴答清单API基础URL
	BASE_URL: "https://api.dida365.com",
	
	// 登录相关URL
	LOGIN_URL: "https://api.dida365.com/api/v2/user/signon?wc=true&remember=true",
	
	// 批量数据获取URL
	BATCH_DATA_URL: "/api/v2/batch/check/0",
	
	// 任务相关URL
	TASK_URL: "/api/v2/task",
	
	// 项目相关URL
	PROJECT_URL: "/api/v2/project",
	
	// 标签相关URL
	TAG_URL: "/api/v2/tag",
	
	// 请求超时时间（毫秒）
	TIMEOUT: 10000,
	
	// 默认请求头
	DEFAULT_HEADERS: {
		"Accept": "application/json",
		"User-Agent": "Apifox/1.0.0",
		"Content-Type": "application/json",
		"Host": "api.dida365.com",
		"Accept-Encoding": "gzip, deflate, br",
		"Connection": "keep-alive"
	},
	
	// 登录请求头
	LOGIN_HEADERS: {
		"Content-Type": "application/json",
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Accept": "*/*",
		"Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
		"Origin": "https://dida365.com",
		"Referer": "https://dida365.com/"
	}
}

// 任务相关常量
const TASK_CONFIG = {
	// 任务状态
	STATUS: {
		ACTIVE: 0,      // 活跃
		COMPLETED: 2    // 已完成
	},
	
	// 任务优先级
	PRIORITY: {
		LOWEST: 0,      // 最低
		LOW: 1,         // 低
		MEDIUM: 3,      // 中
		HIGH: 5         // 高
	},
	
	// 任务类型
	KIND: {
		TEXT: "TEXT",           // 文本任务
		CHECKLIST: "CHECKLIST", // 清单任务
		NOTE: "NOTE"            // 笔记任务
	},
	
	// 任务筛选模式
	FILTER_MODE: {
		ALL: "all",                    // 全部
		TODAY: "today",                // 今天
		YESTERDAY: "yesterday",        // 昨天
		RECENT_7_DAYS: "recent_7_days" // 最近7天
	}
}

// 项目相关常量
const PROJECT_CONFIG = {
	// 项目类型
	KIND: {
		TASK: "TASK",       // 任务项目
		NOTE: "NOTE"        // 笔记项目
	},
	
	// 项目颜色
	COLORS: [
		"#3498db", // 蓝色
		"#e74c3c", // 红色
		"#2ecc71", // 绿色
		"#f39c12", // 橙色
		"#9b59b6", // 紫色
		"#1abc9c", // 青色
		"#34495e", // 深灰色
		"#95a5a6"  // 浅灰色
	]
}

// 错误码定义
const ERROR_CODES = {
	// 参数错误
	PARAM_IS_NULL: "PARAM_IS_NULL",
	PARAM_INVALID: "PARAM_INVALID",
	
	// 认证错误
	UNAUTHORIZED: "UNAUTHORIZED",
	LOGIN_ERROR: "LOGIN_ERROR",
	TOKEN_NOT_FOUND: "TOKEN_NOT_FOUND",
	
	// 网络错误
	NETWORK_ERROR: "NETWORK_ERROR",
	TIMEOUT_ERROR: "TIMEOUT_ERROR",
	API_ERROR: "API_ERROR",
	
	// 数据错误
	PARSE_ERROR: "PARSE_ERROR",
	DATA_NOT_FOUND: "DATA_NOT_FOUND",
	
	// 业务错误
	PROJECT_NOT_FOUND: "PROJECT_NOT_FOUND",
	TASK_NOT_FOUND: "TASK_NOT_FOUND",
	TAG_NOT_FOUND: "TAG_NOT_FOUND",
	
	// 未知错误
	UNKNOWN_ERROR: "UNKNOWN_ERROR"
}

// 成功响应模板
const SUCCESS_RESPONSE = {
	errCode: null,
	errMsg: "操作成功",
	data: null
}

// 错误响应模板
const ERROR_RESPONSE = {
	errCode: "UNKNOWN_ERROR",
	errMsg: "操作失败",
	details: null
}

module.exports = {
	API_CONFIG,
	TASK_CONFIG,
	PROJECT_CONFIG,
	ERROR_CODES,
	SUCCESS_RESPONSE,
	ERROR_RESPONSE
}
