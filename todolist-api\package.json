{"name": "todolist-api", "version": "1.0.0", "description": "滴答清单API云对象，提供完整的滴答清单功能接口", "main": "index.obj.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["todolist", "dida365", "unicloud", "cloud-object", "task-management"], "author": "Your Name", "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/todolist-api.git"}, "bugs": {"url": "https://github.com/your-username/todolist-api/issues"}, "homepage": "https://github.com/your-username/todolist-api#readme"}