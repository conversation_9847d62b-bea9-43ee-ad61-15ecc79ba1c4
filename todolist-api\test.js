/**
 * 滴答清单API云对象测试文件
 * 用于测试各个云对象的功能是否正常
 */

// 模拟uniCloud环境（实际使用时不需要）
const mockUniCloud = {
	httpclient: {
		async request(url, options) {
			// 模拟HTTP请求
			console.log(`模拟请求: ${options.method} ${url}`);
			console.log("请求选项:", options);

			// 模拟登录响应
			if (url.includes("/user/signon")) {
				return {
					status: 200,
					headers: {
						"set-cookie": ["t=mock-token-12345; Path=/; HttpOnly"],
					},
					data: JSON.stringify({
						success: true,
						message: "登录成功",
					}),
				};
			}

			// 模拟批量数据响应
			if (url.includes("/batch/check")) {
				return {
					status: 200,
					data: JSON.stringify({
						syncTaskBean: {
							update: [
								{
									id: "task-1",
									title: "测试任务1",
									content: "这是一个测试任务",
									priority: 3,
									status: 0,
									kind: "TEXT",
									projectId: "project-1",
									createdTime: new Date().toISOString(),
									modifiedTime: new Date().toISOString(),
								},
							],
						},
						projectProfiles: [
							{
								id: "project-1",
								name: "测试项目",
								color: "#3498db",
								kind: "TASK",
								closed: false,
								createdTime: new Date().toISOString(),
								modifiedTime: new Date().toISOString(),
							},
						],
						tags: [
							{
								id: "tag-1",
								name: "重要",
								color: "#e74c3c",
							},
						],
					}),
				};
			}

			// 模拟任务创建响应
			if (url.includes("/task") && options.method === "POST") {
				return {
					status: 200,
					data: JSON.stringify({
						id: "new-task-id",
						title: options.data.title,
						content: options.data.content,
						priority: options.data.priority,
						status: 0,
						kind: "TEXT",
						projectId: options.data.projectId,
						createdTime: new Date().toISOString(),
						modifiedTime: new Date().toISOString(),
					}),
				};
			}

			// 默认成功响应
			return {
				status: 200,
				data: JSON.stringify({ success: true }),
			};
		},
	},
};

// 模拟云对象环境
function createMockCloudObject(objModule) {
	const obj = { ...objModule };

	// 模拟云对象方法
	obj.getClientInfo = () => ({ clientIP: "127.0.0.1" });
	obj.getMethodName = () => "testMethod";

	// 执行_before方法
	if (obj._before) {
		obj._before.call(obj);
	}

	return obj;
}

// 测试主函数
async function runTests() {
	console.log("=== 滴答清单API云对象测试开始 ===\n");

	// 设置全局uniCloud模拟
	global.uniCloud = mockUniCloud;

	try {
		// 测试主云对象
		console.log("1. 测试主云对象登录功能");
		const mainObj = createMockCloudObject(require("./index.obj.js"));

		const loginResult = await mainObj.login(
			"13800138000",
			"testpassword",
			true
		);
		console.log("登录结果:", loginResult);

		if (loginResult.errCode) {
			console.error("❌ 登录测试失败");
			return;
		} else {
			console.log("✅ 登录测试成功");
		}

		const token = loginResult.data.token;
		console.log("获得Token:", token);
		console.log();

		// 测试任务管理功能
		console.log("2. 测试任务管理功能");

		// 获取任务列表
		const tasksResult = await mainObj.getTasks();
		console.log("任务列表结果:", tasksResult);

		if (tasksResult.errCode) {
			console.error("❌ 获取任务列表失败");
		} else {
			console.log("✅ 获取任务列表成功，任务数量:", tasksResult.data.length);
		}

		// 创建任务
		const createTaskResult = await mainObj.createTask(
			"测试任务",
			"这是通过云对象创建的测试任务",
			3,
			"测试项目"
		);
		console.log("创建任务结果:", createTaskResult);

		if (createTaskResult.errCode) {
			console.error("❌ 创建任务失败");
		} else {
			console.log("✅ 创建任务成功");
		}
		console.log();

		// 测试项目管理功能
		console.log("3. 测试项目管理功能");

		// 获取项目列表
		const projectsResult = await mainObj.getProjects();
		console.log("项目列表结果:", projectsResult);

		if (projectsResult.errCode) {
			console.error("❌ 获取项目列表失败");
		} else {
			console.log("✅ 获取项目列表成功，项目数量:", projectsResult.data.length);
		}

		// 创建项目
		const createProjectResult = await mainObj.createProject(
			"新测试项目",
			"#e74c3c",
			"TASK"
		);
		console.log("创建项目结果:", createProjectResult);

		if (createProjectResult.errCode) {
			console.error("❌ 创建项目失败");
		} else {
			console.log("✅ 创建项目成功");
		}
		console.log();

		// 测试工具函数
		console.log("4. 测试工具函数");
		const utils = require("./utils.js");

		// 测试参数校验
		const validationResult = utils.validateParams({ name: "test" }, [
			"name",
			"email",
		]);
		if (validationResult) {
			console.log("✅ 参数校验功能正常:", validationResult.errMsg);
		}

		// 测试日期格式化
		const formattedDate = utils.formatDateForApi("2024-01-01 12:00");
		console.log("✅ 日期格式化功能正常:", formattedDate);

		// 测试成功响应创建
		const successResponse = utils.createSuccessResponse("测试成功", {
			test: true,
		});
		console.log("✅ 成功响应创建功能正常:", successResponse);

		console.log("\n=== 所有测试完成 ===");
		console.log("✅ 云对象功能基本正常，可以部署到uniCloud环境进行实际测试");
	} catch (error) {
		console.error("❌ 测试过程中发生异常:", error);
	}
}

// 运行测试
if (require.main === module) {
	runTests();
}

module.exports = {
	runTests,
	createMockCloudObject,
};
